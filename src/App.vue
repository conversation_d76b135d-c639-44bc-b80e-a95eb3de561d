<template>
  <div>
    <MyButton ref="buttonRef">Test Button</MyButton>
    <MyButton plain>Plain Button</MyButton>
    <MyButton round>Round Button</MyButton>
    <MyButton circle>Circle Button</MyButton>
    <MyButton disabled>Disabled <PERSON><PERSON></MyButton>
    <MyButton type="primary">Primary Button</MyButton>
    <MyButton type="success">Success Button</MyButton>
    <MyButton type="warning">Warning Button</MyButton>
    <MyButton type="danger">Danger Button</MyButton>
    <MyButton type="info">Info Button</MyButton>
    <MyButton type="primary" size="large">Large Button</MyButton>
    <MyButton type="primary" size="small">Small Button</MyButton>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import MyButton from './components/Button/Button.vue'
import type { ButtonInstance } from './components/Button/types'

const buttonRef = ref<ButtonInstance | null>(null)

onMounted(() => {
  if (buttonRef.value) {
    console.log(buttonRef.value.ref)
  }
})
</script>

<style lang="scss" scoped></style>
