:root {
  /* colors */
  --vk-color-white: #ffffff;
  --vk-color-black: #000000;
  --colors: (
    primary: #409eff,
    success: #67c23a,
    warning: #e6a23c,
    danger: #f56c6c,
    info: #909399
  );

  @each $val, $color in var(--colors) {
    --vk-color-$(val): $(color);
    @for $i from 3 to 9 by 2 {
      --vk-color-$(val)-light-$(i): mix(#fff, $(color), .$(i))
    }
    --vk-color-$(val)-light-8: mix(#fff, $(color), .8);
    --vk-color-$(val)-dark-2: mix(#000, $(color), .2);
  }

  --vk-bg-color: #ffffff;
  --vk-bg-color-page: #f2f3f5;
  --vk-bg-color-overlay: #ffffff;
  --vk-text-color-primary: #303133;
  --vk-text-color-regular: #606266;
  --vk-text-color-secondary: #909399;
  --vk-text-color-placeholder: #a8abb2;
  --vk-text-color-disabled: #c0c4cc;
  --vk-border-color: #dcdfe6;
  --vk-border-color-light: #e4e7ed;
  --vk-border-color-lighter: #ebeef5;
  --vk-border-color-extra-light: #f2f6fc;
  --vk-border-color-dark: #d4d7de;
  --vk-border-color-darker: #cdd0d6;
  --vk-fill-color: #f0f2f5;
  --vk-fill-color-light: #f5f7fa;
  --vk-fill-color-lighter: #fafafa;
  --vk-fill-color-extra-light: #fafcff;
  --vk-fill-color-dark: #ebedf0;
  --vk-fill-color-darker: #e6e8eb;
  --vk-fill-color-blank: #ffffff;

  /* border */
  --vk-border-width: 1px;
  --vk-border-style: solid;
  --vk-border-color-hover: var(--vk-text-color-disabled);
  --vk-border: var(--vk-border-width) var(--vk-border-style) var(--vk-border-color);
  --vk-border-radius-base: 4px;
  --vk-border-radius-small: 2px;
  --vk-border-radius-round: 20px;
  --vk-border-radius-circle: 100%;

  /*font*/
  --vk-font-size-extra-large: 20px;
  --vk-font-size-large: 18px;
  --vk-font-size-medium: 16px;
  --vk-font-size-base: 14px;
  --vk-font-size-small: 13px;
  --vk-font-size-extra-small: 12px;
  --vk-font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "\5fae\8f6f\96c5\9ed1", Arial, sans-serif;
  --vk-font-weight-primary: 500;

  /*disabled*/
  --vk-disabled-bg-color: var(--vk-fill-color-light);
  --vk-disabled-text-color: var(--vk-text-color-placeholder);
  --vk-disabled-border-color: var(--vk-border-color-light);
  
  /*animation*/
  --vk-transition-duration: .3s;
  --vk-transition-duration-fast: .2s;

}